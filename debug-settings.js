// Debug script to test settings page functionality
const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging Settings Page...\n');

// Check if agent-settings.json exists and is readable
const settingsPath = path.join(__dirname, 'src', 'lib', 'agent-settings.json');
console.log('📁 Settings file path:', settingsPath);
console.log('📁 File exists:', fs.existsSync(settingsPath));

if (fs.existsSync(settingsPath)) {
  try {
    const content = fs.readFileSync(settingsPath, 'utf-8');
    const agents = JSON.parse(content);
    
    console.log('\n📊 Agents loaded from settings file:');
    agents.forEach((agent, index) => {
      console.log(`${index + 1}. ${agent.name} (ID: ${agent.id})`);
    });
    
    // Check specifically for <PERSON> and <PERSON>
    const cody = agents.find(a => a.name === 'Cody');
    const li = agents.find(a => a.name === 'Li');
    
    console.log('\n🔍 Specific agent check:');
    console.log('<PERSON> found:', cody ? '✅' : '❌');
    console.log('<PERSON> found:', li ? '✅' : '❌');
    
    if (cody) {
      console.log('Cody details:', {
        id: cody.id,
        name: cody.name,
        icon: cody.icon,
        model: cody.model
      });
    }
    
    if (li) {
      console.log('Li details:', {
        id: li.id,
        name: li.name,
        icon: li.icon,
        model: li.model
      });
    }
    
  } catch (error) {
    console.error('❌ Error parsing settings file:', error);
  }
} else {
  console.log('❌ Settings file not found');
}

// Check the data.ts file
const dataPath = path.join(__dirname, 'src', 'lib', 'data.ts');
console.log('\n📁 Data file path:', dataPath);
console.log('📁 Data file exists:', fs.existsSync(dataPath));

console.log('\n💡 Next steps:');
console.log('1. Open browser dev tools on the settings page');
console.log('2. Check for JavaScript errors in console');
console.log('3. Verify that agent buttons are clickable');
console.log('4. Check if selectedAgent state is updating');
