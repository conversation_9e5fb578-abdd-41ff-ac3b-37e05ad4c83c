// Test multiple LLM providers configuration
require('dotenv').config();

async function testModelProviders() {
  console.log('🔍 Testing Multi-Provider Model Configuration...\n');
  
  // Test environment variables
  console.log('📋 Environment Variables Check:');
  const envVars = [
    { name: 'GEMINI_API_KEY', value: process.env.GEMINI_API_KEY },
    { name: 'OPENAI_API_KEY', value: process.env.OPENAI_API_KEY },
    { name: 'GROQ_API_KEY', value: process.env.GROQ_API_KEY },
    { name: 'ANTHROPIC_API_KEY', value: process.env.ANTHROPIC_API_KEY },
    { name: 'DEEPSEEK_API_KEY', value: process.env.DEEPSEEK_API_KEY },
    { name: 'OPENROUTER_API_KEY', value: process.env.OPENROUTER_API_KEY }
  ];
  
  envVars.forEach(({ name, value }) => {
    const status = value ? '✅' : '❌';
    const display = value ? `${value.substring(0, 10)}...` : 'Not set';
    console.log(`  ${status} ${name}: ${display}`);
  });
  
  console.log('\n🔧 Testing Ollama Connection:');
  try {
    const response = await fetch('http://localhost:11434/api/tags');
    if (response.ok) {
      const data = await response.json();
      console.log('  ✅ Ollama is running');
      console.log(`  📊 Available models: ${data.models?.length || 0}`);
      if (data.models && data.models.length > 0) {
        console.log('  📝 Sample models:');
        data.models.slice(0, 3).forEach(model => {
          console.log(`    - ${model.name} (${(model.size / 1024 / 1024 / 1024).toFixed(1)}GB)`);
        });
      }
    } else {
      console.log('  ❌ Ollama not responding');
    }
  } catch (error) {
    console.log('  ❌ Ollama connection failed:', error.message);
    console.log('  💡 Make sure Ollama is running: docker run -d -p 11434:11434 ollama/ollama');
  }
  
  console.log('\n🎯 Model Provider Summary:');
  
  const providers = [
    {
      name: 'Google AI (Gemini)',
      available: !!process.env.GEMINI_API_KEY,
      models: ['gemini-1.5-flash-latest', 'gemini-1.5-pro-latest', 'gemini-2.0-flash-exp'],
      strengths: ['Multimodal', 'Large context', 'Fast']
    },
    {
      name: 'OpenAI (GPT)',
      available: !!process.env.OPENAI_API_KEY,
      models: ['gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo'],
      strengths: ['High quality', 'Tool calling', 'Vision']
    },
    {
      name: 'Groq (Fast Inference)',
      available: !!process.env.GROQ_API_KEY,
      models: ['llama-3.1-70b-versatile', 'llama-3.1-8b-instant', 'mixtral-8x7b-32768'],
      strengths: ['Ultra-fast inference', 'Cost-effective', 'Open models']
    },
    {
      name: 'Anthropic (Claude)',
      available: !!process.env.ANTHROPIC_API_KEY,
      models: ['claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022'],
      strengths: ['Excellent reasoning', 'Safety-focused', 'Long context']
    },
    {
      name: 'DeepSeek',
      available: !!process.env.DEEPSEEK_API_KEY,
      models: ['deepseek-chat', 'deepseek-coder'],
      strengths: ['Low cost', 'Coding specialized', 'Good reasoning']
    },
    {
      name: 'Ollama (Local)',
      available: true, // Always available if running
      models: ['llama3.1:8b', 'llama3.1:70b', 'gemma2:9b', 'qwen2.5:7b', 'codellama:7b'],
      strengths: ['No API costs', 'Privacy', 'Offline capable']
    }
  ];
  
  providers.forEach(provider => {
    const status = provider.available ? '✅' : '❌';
    console.log(`\n  ${status} ${provider.name}`);
    console.log(`     Models: ${provider.models.join(', ')}`);
    console.log(`     Strengths: ${provider.strengths.join(', ')}`);
  });
  
  console.log('\n🚀 Agent Configuration Examples:');
  console.log(`
  // Fast and cost-effective (Groq)
  "model": {
    "provider": "groq",
    "modelId": "llama-3.1-8b-instant"
  }
  
  // High quality reasoning (Anthropic)
  "model": {
    "provider": "anthropic", 
    "modelId": "claude-3-5-haiku-20241022"
  }
  
  // Multimodal capabilities (Google)
  "model": {
    "provider": "googleai",
    "modelId": "gemini-1.5-flash-latest"
  }
  
  // Local and private (Ollama)
  "model": {
    "provider": "ollama",
    "modelId": "llama3.1:8b"
  }
  
  // Coding specialized (DeepSeek)
  "model": {
    "provider": "deepseek",
    "modelId": "deepseek-coder"
  }
  `);
  
  console.log('\n✅ Multi-Provider Setup Complete!');
  console.log('🎯 You can now use different models for different agents based on their needs:');
  console.log('   • Cody (coding) → DeepSeek Coder or Groq Llama for fast responses');
  console.log('   • Amanda (therapy) → Claude for empathetic reasoning');
  console.log('   • Li (email) → GPT-4o Mini for professional writing');
  console.log('   • Scout (job search) → Gemini for multimodal resume analysis');
  console.log('   • Local agents → Ollama models for privacy');
}

testModelProviders().catch(console.error);
