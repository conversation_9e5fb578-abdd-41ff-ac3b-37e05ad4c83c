// Test Cody's agent tools directly
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Simulate the codyKnowledgeBaseTool function
async function testCodyKnowledgeBaseTool(query) {
  console.log(`🔍 Testing Cody's Knowledge Base Tool with query: "${query}"`);
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return 'Configuration Error: Supabase URL or Service Role Key is not configured. Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file.';
  }
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    // Generate embedding using Ollama (matching the actual tool implementation)
    const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'nomic-embed-text:latest',
        prompt: query
      })
    });

    if (!embeddingResponse.ok) {
      throw new Error(`Ollama API error: ${embeddingResponse.status} ${embeddingResponse.statusText}`);
    }

    const embeddingData = await embeddingResponse.json();
    const embedding = embeddingData.embedding;

    if (!embedding || !Array.isArray(embedding)) {
      throw new Error('Invalid embedding response from Ollama');
    }

    // Search the knowledge base using the RPC function
    const { data, error } = await supabase.rpc('match_coding_database', {
      query_embedding: embedding,
      match_threshold: 0.3, // Lowered from 0.75 to find more relevant results
      match_count: 5,
    });

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      return 'No relevant information found in the knowledge base for that query.';
    }

    const formattedResults = data
      .map((item) => `- ${item.content}`)
      .join('\n');
      
    return `Found the following information in the knowledge base:\n${formattedResults}`;

  } catch (error) {
    const errorMessage = error.message || 'Unknown error';
    
    if (errorMessage.includes('function match_coding_database')) {
      return `Database Function Error: The match_coding_database function does not exist in your Supabase database. Please ensure the RPC function is created with the correct signature.`;
    }
    
    if (errorMessage.includes('different vector dimensions')) {
      return `Embedding Dimension Error: There's a mismatch between the embedding dimensions. Your database expects 768-dimensional vectors from nomic-embed-text, but received different dimensions.`;
    }
    
    if (errorMessage.includes('Ollama API error')) {
      return `Ollama Connection Error: Could not connect to Ollama at http://localhost:11434. Please ensure Ollama is running and the nomic-embed-text model is installed.`;
    }
    
    if (errorMessage.includes('failed to fetch')) {
      return `Supabase Connection Error: The tool could not connect to your Supabase instance. Please verify that your NEXT_PUBLIC_SUPABASE_URL is correct in the .env file and that your database is running.`;
    }

    return `An unexpected error occurred while searching the knowledge base: ${errorMessage}`;
  }
}

// Test different types of queries
async function runTests() {
  console.log('🚀 Testing Cody\'s Knowledge Base Tool Implementation\n');
  
  const testQueries = [
    'How do I create a Pydantic AI agent?',
    'How to integrate Supabase with n8n?',
    'JavaScript API best practices',
    'Python database connection patterns'
  ];
  
  for (const query of testQueries) {
    try {
      const result = await testCodyKnowledgeBaseTool(query);
      console.log(`✅ Result for "${query}":`);
      console.log(result.substring(0, 300) + (result.length > 300 ? '...' : ''));
      console.log('\n' + '='.repeat(80) + '\n');
    } catch (error) {
      console.log(`❌ Error for "${query}": ${error.message}\n`);
    }
  }
}

runTests();
