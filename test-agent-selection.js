// Test agent selection issue
require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');

async function testAgentSelection() {
  console.log('🔍 Testing Agent Selection Issue...\n');
  
  try {
    // Load agent settings
    const settingsPath = path.join(__dirname, 'src', 'lib', 'agent-settings.json');
    const settingsContent = await fs.readFile(settingsPath, 'utf-8');
    const agents = JSON.parse(settingsContent);
    
    console.log('📊 All Agents in Configuration:');
    agents.forEach((agent, index) => {
      console.log(`\n${index + 1}. ${agent.name}`);
      console.log(`   ID: ${agent.id}`);
      console.log(`   Icon: ${agent.icon}`);
      console.log(`   Model: ${agent.model.provider}/${agent.model.modelId}`);
      console.log(`   Tools: ${agent.tools?.length || 0}`);
      console.log(`   Description: ${agent.description.substring(0, 60)}...`);
    });
    
    // Check for specific issues with <PERSON> and <PERSON>
    console.log('\n🔍 Detailed Analysis of Cody and Li:');
    
    const cody = agents.find(a => a.name === '<PERSON>');
    const li = agents.find(a => a.name === 'Li');
    
    if (cody) {
      console.log('\n📝 Cody Analysis:');
      console.log(`   ✅ Found in agents array`);
      console.log(`   ✅ Has valid ID: ${cody.id}`);
      console.log(`   ✅ Has valid name: ${cody.name}`);
      console.log(`   ✅ Has valid icon: ${cody.icon}`);
      console.log(`   ✅ Has model config: ${cody.model.provider}/${cody.model.modelId}`);
      console.log(`   ✅ Has tools: ${cody.tools?.length || 0}`);
      console.log(`   ✅ Has system message: ${cody.systemMessage ? 'Yes' : 'No'}`);
      console.log(`   ✅ Has description: ${cody.description ? 'Yes' : 'No'}`);
      
      // Check for any potential JSON issues
      try {
        JSON.stringify(cody);
        console.log(`   ✅ JSON serializable: Yes`);
      } catch (e) {
        console.log(`   ❌ JSON serializable: No - ${e.message}`);
      }
    } else {
      console.log('\n❌ Cody not found in agents array!');
    }
    
    if (li) {
      console.log('\n📝 Li Analysis:');
      console.log(`   ✅ Found in agents array`);
      console.log(`   ✅ Has valid ID: ${li.id}`);
      console.log(`   ✅ Has valid name: ${li.name}`);
      console.log(`   ✅ Has valid icon: ${li.icon}`);
      console.log(`   ✅ Has model config: ${li.model.provider}/${li.model.modelId}`);
      console.log(`   ✅ Has tools: ${li.tools?.length || 0}`);
      console.log(`   ✅ Has system message: ${li.systemMessage ? 'Yes' : 'No'}`);
      console.log(`   ✅ Has description: ${li.description ? 'Yes' : 'No'}`);
      
      // Check for any potential JSON issues
      try {
        JSON.stringify(li);
        console.log(`   ✅ JSON serializable: Yes`);
      } catch (e) {
        console.log(`   ❌ JSON serializable: No - ${e.message}`);
      }
    } else {
      console.log('\n❌ Li not found in agents array!');
    }
    
    // Check for duplicate IDs
    console.log('\n🔍 Checking for Duplicate IDs:');
    const ids = agents.map(a => a.id);
    const uniqueIds = [...new Set(ids)];
    
    if (ids.length === uniqueIds.length) {
      console.log('   ✅ No duplicate IDs found');
    } else {
      console.log('   ❌ Duplicate IDs found!');
      const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);
      duplicates.forEach(id => {
        const duplicateAgents = agents.filter(a => a.id === id);
        console.log(`     Duplicate ID ${id}: ${duplicateAgents.map(a => a.name).join(', ')}`);
      });
    }
    
    // Check for missing required fields
    console.log('\n🔍 Checking Required Fields:');
    const requiredFields = ['id', 'name', 'description', 'systemMessage', 'icon', 'model'];
    
    agents.forEach(agent => {
      const missing = requiredFields.filter(field => !agent[field]);
      if (missing.length === 0) {
        console.log(`   ✅ ${agent.name}: All required fields present`);
      } else {
        console.log(`   ❌ ${agent.name}: Missing fields: ${missing.join(', ')}`);
      }
    });
    
    // Test model provider availability
    console.log('\n🔍 Model Provider Analysis:');
    const providerStatus = {
      'googleai': !!process.env.GEMINI_API_KEY,
      'openai': !!process.env.OPENAI_API_KEY,
      'groq': !!process.env.GROQ_API_KEY,
      'anthropic': !!process.env.ANTHROPIC_API_KEY,
      'ollama': true // Assume available
    };
    
    agents.forEach(agent => {
      const provider = agent.model.provider;
      const isConfigured = providerStatus[provider];
      const status = isConfigured ? '✅' : '❌';
      console.log(`   ${status} ${agent.name}: ${provider} (${isConfigured ? 'configured' : 'not configured'})`);
    });
    
    console.log('\n🎯 Summary:');
    console.log(`   Total agents: ${agents.length}`);
    console.log(`   Cody found: ${cody ? '✅' : '❌'}`);
    console.log(`   Li found: ${li ? '✅' : '❌'}`);
    console.log(`   All agents have required fields: ${agents.every(a => requiredFields.every(f => a[f])) ? '✅' : '❌'}`);
    console.log(`   No duplicate IDs: ${ids.length === uniqueIds.length ? '✅' : '❌'}`);
    
    console.log('\n💡 If Cody and Li still cannot be selected:');
    console.log('   1. Check browser console for JavaScript errors');
    console.log('   2. Verify the onClick handlers are being called');
    console.log('   3. Check if there are any CSS issues preventing clicks');
    console.log('   4. Ensure the agent state is being updated correctly');
    
  } catch (error) {
    console.log('❌ Error during agent selection test:', error.message);
  }
}

testAgentSelection().catch(console.error);
