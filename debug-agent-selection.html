<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Agent Selection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .agent-button { 
            display: block; 
            width: 200px; 
            padding: 10px; 
            margin: 5px 0; 
            border: 1px solid #ccc; 
            background: #f9f9f9; 
            cursor: pointer; 
        }
        .agent-button:hover { background: #e9e9e9; }
        .agent-button.selected { background: #007bff; color: white; }
        .debug-info { 
            margin-top: 20px; 
            padding: 10px; 
            background: #f0f0f0; 
            border: 1px solid #ddd; 
        }
    </style>
</head>
<body>
    <h1>Agent Selection Debug Test</h1>
    <p>This test simulates the agent selection functionality to identify issues.</p>
    
    <div id="agent-list"></div>
    <div id="debug-info" class="debug-info"></div>

    <script>
        // Simulate the agent data
        const agents = [
            { id: "NeK3H8lDKNXHFHVV", name: "<PERSON>", icon: "Heart" },
            { id: "Mq57tleDBanDxipt", name: "Cody", icon: "Code" },
            { id: "x3M1ATR5WY16vkPd", name: "Li", icon: "Mail" },
            { id: "k1C6YbeQ5QW2vlSp", name: "Orion", icon: "Bot" },
            { id: "PfUSVNwHXQy7c6C8", name: "Rob", icon: "Smile" },
            { id: "4jn4JiOzQZuclHRD", name: "Scout", icon: "Briefcase" }
        ];

        let selectedAgent = null;

        function renderAgents() {
            const agentList = document.getElementById('agent-list');
            agentList.innerHTML = '';

            agents.forEach(agent => {
                const button = document.createElement('button');
                button.className = 'agent-button';
                button.textContent = `${agent.name} (${agent.icon})`;
                button.onclick = () => selectAgent(agent);
                
                if (selectedAgent && selectedAgent.id === agent.id) {
                    button.classList.add('selected');
                }
                
                agentList.appendChild(button);
            });
        }

        function selectAgent(agent) {
            console.log('Selecting agent:', agent.name, agent.id);
            selectedAgent = agent;
            updateDebugInfo();
            renderAgents(); // Re-render to show selection
        }

        function updateDebugInfo() {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.innerHTML = `
                <h3>Debug Information</h3>
                <p><strong>Selected Agent:</strong> ${selectedAgent ? selectedAgent.name : 'None'}</p>
                <p><strong>Selected ID:</strong> ${selectedAgent ? selectedAgent.id : 'None'}</p>
                <p><strong>Total Agents:</strong> ${agents.length}</p>
                <p><strong>Last Click Time:</strong> ${new Date().toLocaleTimeString()}</p>
                <h4>All Agents:</h4>
                <ul>
                    ${agents.map(agent => `
                        <li>
                            ${agent.name} (${agent.id}) 
                            ${selectedAgent && selectedAgent.id === agent.id ? '← SELECTED' : ''}
                        </li>
                    `).join('')}
                </ul>
            `;
        }

        // Test specific agents
        function testCodySelection() {
            const cody = agents.find(a => a.name === 'Cody');
            console.log('Testing Cody selection:', cody);
            selectAgent(cody);
        }

        function testLiSelection() {
            const li = agents.find(a => a.name === 'Li');
            console.log('Testing Li selection:', li);
            selectAgent(li);
        }

        // Initialize
        renderAgents();
        updateDebugInfo();

        // Add test buttons
        const testDiv = document.createElement('div');
        testDiv.innerHTML = `
            <h3>Manual Tests</h3>
            <button onclick="testCodySelection()">Select Cody</button>
            <button onclick="testLiSelection()">Select Li</button>
            <button onclick="selectAgent(agents[0])">Select Amanda</button>
        `;
        document.body.appendChild(testDiv);

        console.log('Debug test loaded. Agents:', agents);
    </script>
</body>
</html>
