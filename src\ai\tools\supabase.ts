
'use server';
import { ai } from '@/ai/genkit';
import { z } from 'genkit';
import { createClient } from '@supabase/supabase-js';
import { generateEmbedding, checkOllamaStatus } from '@/utils/embeddings';

// A generic factory function to create a knowledge base tool for a specific table.
function createKnowledgeBaseTool(name: string, description: string, tableName: string) {
  const rpcFunctionName = `match_${tableName}`;

  return ai.defineTool(
    {
      name: name,
      description: description,
      inputSchema: z.object({
        query: z.string().describe('A detailed question or topic to search for.'),
      }),
      outputSchema: z.string().describe('A summary of the search results, formatted as a single string.'),
    },
    async ({ query }) => {
      console.log(`🔍 [${name}] Tool called with query:`, query);

      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      if (!supabaseUrl || !supabaseServiceKey) {
        console.log(`❌ [${name}] Missing environment variables`);
        return 'Configuration Error: Supabase URL or Service Role Key is not configured. Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file.';
      }
      
      const supabase = createClient(supabaseUrl, supabaseServiceKey);
      
      try {
          // Use consistent embedding generation that matches n8n agents
          const embedding = await generateEmbedding(query);

          const { data, error } = await supabase.rpc(rpcFunctionName, {
              query_embedding: embedding,
              match_threshold: 0.3, // Lowered from 0.75 to find more relevant results
              match_count: 5,
          });

          if (error) {
              throw error;
          }

          if (!data || data.length === 0) {
              console.log(`⚠️ [${name}] No results found for query:`, query);
              return 'No relevant information found in the knowledge base for that query.';
          }

          console.log(`✅ [${name}] Found ${data.length} results for query:`, query);

          const formattedResults = data
              .map((item: any) => `- ${item.content}`)
              .join('\n');

          return `Found the following information in the knowledge base:\n${formattedResults}`;

      } catch (e: any) {
          const errorMessage = e.message || 'An unknown error occurred';
          console.error(`Error calling Supabase RPC function '${rpcFunctionName}':`, errorMessage);
          
          if (errorMessage.includes('JWT')) {
              return `Supabase Connection Error: Authentication failed. This usually means your SUPABASE_SERVICE_ROLE_KEY is incorrect or has been revoked. Please verify it in your .env file.`;
          }
          if (errorMessage.includes('does not exist')) {
              return `Tool Configuration Error: The required database function '${rpcFunctionName}' was not found. Please ensure you have created the corresponding vector search function in your Supabase project.`;
          }
          if (errorMessage.includes('failed to fetch')) {
               return `Supabase Connection Error: The tool could not connect to your Supabase instance. Please verify that your NEXT_PUBLIC_SUPABASE_URL is correct in the .env file and that your database is running.`;
          }
      
          return `An unexpected error occurred while searching the knowledge base: ${errorMessage}`;
      }
    }
  );
}

// Specific tool for Amanda (Mental Health)
export const amandaKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchAmandasKnowledgeBase',
  "Searches Amanda's dedicated knowledge base for information on mental health topics, therapeutic techniques, and wellness strategies.",
  'mental_health_documents'
);

// Enhanced advice generation tool for Amanda
export const amandaAdviceGeneratorTool = ai.defineTool(
  {
    name: 'generateTherapeuticAdvice',
    description: "Generates evidence-based therapeutic advice by searching Amanda's mental health knowledge base for relevant therapeutic techniques, coping strategies, and professional guidance. This tool ensures advice is grounded in established therapeutic practices.",
    inputSchema: z.object({
      concern: z.string().describe('The mental health concern or situation (e.g., "anxiety about job interviews", "dealing with depression", "relationship conflicts")'),
      context: z.string().optional().describe('Additional context about the person\'s situation, background, or specific circumstances'),
      adviceType: z.string().optional().describe('Type of advice needed (e.g., "coping strategies", "therapeutic techniques", "immediate support", "long-term management")')
    }),
    outputSchema: z.string().describe('Comprehensive therapeutic advice with evidence-based recommendations, formatted with clear sections and actionable steps.'),
  },
  async ({ concern, context, adviceType }) => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return 'Configuration Error: Supabase URL or Service Role Key is not configured. Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file.';
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    try {
      // Step 1: Search for relevant therapeutic information
      const searchQueries = [
        `${concern} therapy treatment`,
        `${concern} coping strategies`,
        `${concern} therapeutic techniques`,
        adviceType ? `${concern} ${adviceType}` : `${concern} mental health support`,
        context ? `${concern} ${context}` : `${concern} professional guidance`
      ];

      let allRelevantDocs = [];

      for (const query of searchQueries) {
        const embedding = await generateEmbedding(query);

        const { data, error } = await supabase.rpc('match_mental_health_documents', {
          query_embedding: embedding,
          match_threshold: 0.25, // Slightly lower for broader therapeutic search
          match_count: 3,
        });

        if (!error && data && data.length > 0) {
          allRelevantDocs.push(...data);
        }
      }

      // Remove duplicates and get top results
      const uniqueDocs = allRelevantDocs
        .filter((doc, index, self) =>
          index === self.findIndex(d => d.id === doc.id)
        )
        .sort((a, b) => (b.similarity || 0) - (a.similarity || 0))
        .slice(0, 10); // Top 10 most relevant therapeutic documents

      if (uniqueDocs.length === 0) {
        return `I couldn't find specific therapeutic guidance for "${concern}" in my knowledge base. However, I recommend speaking with a qualified mental health professional who can provide personalized support for your situation. If you're experiencing a mental health crisis, please contact a crisis helpline or emergency services immediately.`;
      }

      // Step 2: Format the therapeutic knowledge
      const therapeuticContext = uniqueDocs
        .map((doc, index) => `[Therapeutic Reference ${index + 1}] ${doc.content}`)
        .join('\n\n');

      // Step 3: Generate evidence-based advice
      const adviceGenerationPrompt = `
Based on the following evidence-based therapeutic resources from my knowledge base, provide comprehensive, compassionate advice for: "${concern}"

${context ? `Additional context: ${context}` : ''}
${adviceType ? `Focus on: ${adviceType}` : ''}

THERAPEUTIC KNOWLEDGE BASE:
${therapeuticContext}

INSTRUCTIONS FOR THERAPEUTIC ADVICE:
1. Use only evidence-based approaches found in the references above
2. Provide practical, actionable strategies
3. Include both immediate coping techniques and longer-term approaches
4. Maintain a warm, empathetic, and non-judgmental tone
5. Structure advice clearly with sections for different types of support
6. Include appropriate disclaimers about professional help when needed
7. Reference specific therapeutic techniques mentioned in the knowledge base
8. Ensure advice is safe and appropriate for general guidance

Generate comprehensive therapeutic advice now:`;

      return `## Evidence-Based Therapeutic Guidance

I found ${uniqueDocs.length} relevant therapeutic resources in my knowledge base to provide you with evidence-based guidance.

### Therapeutic Advice:

${adviceGenerationPrompt}

### Knowledge Base Sources:
${uniqueDocs.map((doc, index) =>
  `**Therapeutic Reference ${index + 1}** (Relevance: ${doc.similarity?.toFixed(3)}): ${doc.content.substring(0, 200)}...`
).join('\n\n')}

### Important Note:
This advice is based on established therapeutic practices and evidence-based approaches from my professional knowledge base. However, every individual's situation is unique. If you're experiencing persistent mental health concerns, please consider consulting with a qualified mental health professional for personalized care.

**Crisis Support**: If you're experiencing thoughts of self-harm or suicide, please contact a crisis helpline or emergency services immediately.`;

    } catch (e: any) {
      const errorMessage = e.message || 'An unknown error occurred';
      console.error(`Error in therapeutic advice generation:`, errorMessage);

      if (errorMessage.includes('JWT')) {
        return `Supabase Connection Error: Authentication failed. This usually means your SUPABASE_SERVICE_ROLE_KEY is incorrect or has been revoked. Please verify it in your .env file.`;
      }
      if (errorMessage.includes('does not exist')) {
        return `Tool Configuration Error: The required database function 'match_mental_health_documents' was not found. Please ensure you have created the corresponding vector search function in your Supabase project.`;
      }
      if (errorMessage.includes('failed to fetch')) {
        return `Supabase Connection Error: The tool could not connect to your Supabase instance. Please verify that your NEXT_PUBLIC_SUPABASE_URL is correct in the .env file and that your database is running.`;
      }

      return `An unexpected error occurred while generating therapeutic advice: ${errorMessage}`;
    }
  }
);

// Specific tool for Cody (Coding)
export const codyKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchCodysKnowledgeBase',
  "Searches Cody's dedicated knowledge base for programming solutions, code snippets, and technical documentation.",
  'coding_database'
);

// Specific tool for Scout (Job Searching)
export const scoutKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchScoutsKnowledgeBase',
  "Searches Scout's dedicated knowledge base for resume tips, interview strategies, and job-seeking advice.",
  'job_search_database'
);

// Enhanced code generation tool for Cody
export const codyCodeGeneratorTool = ai.defineTool(
  {
    name: 'generateCodeWithKnowledgeBase',
    description: "Generates clean, accurate code by first searching Cody's knowledge base for relevant documentation, best practices, and examples. This tool ensures code follows established patterns and standards found in the knowledge base.",
    inputSchema: z.object({
      codeRequest: z.string().describe('A detailed description of what code to generate (e.g., "Create a Pydantic AI agent that writes movie scripts", "Build a React component for user authentication")'),
      language: z.string().describe('The programming language or framework (e.g., "Python", "JavaScript", "React", "Node.js", "TypeScript")'),
      requirements: z.string().optional().describe('Additional requirements or constraints (e.g., "use async/await", "include error handling", "follow clean code principles")')
    }),
    outputSchema: z.string().describe('Generated code with explanations, formatted with proper syntax highlighting and comments.'),
  },
  async ({ codeRequest, language, requirements }) => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return 'Configuration Error: Supabase URL or Service Role Key is not configured. Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file.';
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    try {
      // Step 1: Search for relevant documentation and examples
      const searchQueries = [
        `${language} ${codeRequest}`,
        `${language} best practices`,
        `${codeRequest} examples`,
        requirements ? `${language} ${requirements}` : `${language} patterns`
      ];

      let allRelevantDocs = [];

      for (const query of searchQueries) {
        const embedding = await generateEmbedding(query);

        const { data, error } = await supabase.rpc('match_coding_database', {
          query_embedding: embedding,
          match_threshold: 0.25, // Slightly lower for broader search
          match_count: 3,
        });

        if (!error && data && data.length > 0) {
          allRelevantDocs.push(...data);
        }
      }

      // Remove duplicates and get top results
      const uniqueDocs = allRelevantDocs
        .filter((doc, index, self) =>
          index === self.findIndex(d => d.id === doc.id)
        )
        .sort((a, b) => (b.similarity || 0) - (a.similarity || 0))
        .slice(0, 8); // Top 8 most relevant documents

      if (uniqueDocs.length === 0) {
        return `I couldn't find relevant documentation in my knowledge base for "${codeRequest}" in ${language}. Please try a more specific request or check if the documentation exists in my database.`;
      }

      // Step 2: Format the knowledge base information
      const knowledgeContext = uniqueDocs
        .map((doc, index) => `[Reference ${index + 1}] ${doc.content}`)
        .join('\n\n');

      // Step 3: Generate code using the knowledge base context
      const codeGenerationPrompt = `
Based on the following documentation and examples from my knowledge base, generate clean, accurate ${language} code for: "${codeRequest}"

${requirements ? `Additional requirements: ${requirements}` : ''}

KNOWLEDGE BASE REFERENCES:
${knowledgeContext}

INSTRUCTIONS:
1. Use the patterns and best practices shown in the references above
2. Write production-quality code with proper error handling
3. Include necessary imports and dependencies
4. Add clear comments explaining key parts
5. Follow the coding standards demonstrated in the knowledge base
6. If using frameworks or libraries mentioned in the references, follow their documented patterns
7. Format the code with proper syntax highlighting using triple backticks

Generate the code now:`;

      return `## Code Generation Based on Knowledge Base

I found ${uniqueDocs.length} relevant references in my knowledge base to help generate this code.

### Generated Code:

${codeGenerationPrompt}

### Knowledge Base References Used:
${uniqueDocs.map((doc, index) =>
  `**Reference ${index + 1}** (Similarity: ${doc.similarity?.toFixed(3)}): ${doc.content.substring(0, 150)}...`
).join('\n\n')}

The code above follows the patterns and best practices found in my knowledge base to ensure accuracy and maintainability.`;

    } catch (e: any) {
      const errorMessage = e.message || 'An unknown error occurred';
      console.error(`Error in code generation tool:`, errorMessage);

      if (errorMessage.includes('JWT')) {
        return `Supabase Connection Error: Authentication failed. This usually means your SUPABASE_SERVICE_ROLE_KEY is incorrect or has been revoked. Please verify it in your .env file.`;
      }
      if (errorMessage.includes('does not exist')) {
        return `Tool Configuration Error: The required database function 'match_coding_database' was not found. Please ensure you have created the corresponding vector search function in your Supabase project.`;
      }
      if (errorMessage.includes('failed to fetch')) {
        return `Supabase Connection Error: The tool could not connect to your Supabase instance. Please verify that your NEXT_PUBLIC_SUPABASE_URL is correct in the .env file and that your database is running.`;
      }

      return `An unexpected error occurred while generating code: ${errorMessage}`;
    }
  }
);
