import {genkit} from 'genkit';
import {googleAI} from '@genkit-ai/googleai';
import {openAI} from 'genkitx-openai';
import {groq} from 'genkitx-groq';
import {anthropic} from 'genkitx-anthropic';
import {ollama} from 'genkitx-ollama';

const plugins = [];

// Google AI (Gemini)
if (process.env.GEMINI_API_KEY) {
  plugins.push(googleAI({
    apiKey: process.env.GEMINI_API_KEY,
    embedders: ['text-embedding-004']
  }));
}

// OpenAI (GPT models)
if (process.env.OPENAI_API_KEY) {
  plugins.push(openAI({
    apiKey: process.env.OPENAI_API_KEY,
  }));
}

// Groq (Fast inference)
if (process.env.GROQ_API_KEY) {
  plugins.push(groq({
    apiKey: process.env.GROQ_API_KEY,
  }));
}

// Anthropic (Claude models)
if (process.env.ANTHROPIC_API_KEY) {
  plugins.push(anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
  }));
}

// DeepSeek (via OpenAI-compatible API) - Temporarily disabled due to plugin conflict
// TODO: Implement DeepSeek as a separate provider
// if (process.env.DEEPSEEK_API_KEY) {
//   plugins.push(openAI({
//     apiKey: process.env.DEEPSEEK_API_KEY,
//     baseUrl: 'https://api.deepseek.com/v1',
//   }));
// }

// Ollama (Local models)
// Note: Assumes Ollama is running on localhost:11434
plugins.push(ollama({
  serverAddress: 'http://localhost:11434',
}));

export const ai = genkit({
  plugins,
});
