// Test the settings page model configuration
require('dotenv').config();

// Simulate the getAvailableModels function
function getAvailableModels() {
  const SUPPORTED_MODELS = [
    {
      id: 'googleai',
      name: 'Google AI (Gemini)',
      envKey: 'GEMINI_API_KEY',
      models: [
        { id: 'gemini-1.5-flash-latest', name: 'Gemini 1.5 Flash' },
        { id: 'gemini-1.5-pro-latest', name: 'Gemini 1.5 Pro' },
        { id: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash (Experimental)' },
      ],
    },
    {
      id: 'openai',
      name: 'OpenAI (GPT)',
      envKey: 'OPENAI_API_KEY',
      models: [
        { id: 'gpt-4o', name: 'GPT-4o' },
        { id: 'gpt-4o-mini', name: 'GPT-4o Mini' },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
      ],
    },
    {
      id: 'groq',
      name: '<PERSON><PERSON><PERSON> (Fast Inference)',
      envKey: 'GROQ_API_KEY',
      models: [
        { id: 'llama-3.1-70b-versatile', name: '<PERSON><PERSON><PERSON> 3.1 70B' },
        { id: 'llama-3.1-8b-instant', name: 'Llama 3.1 8B' },
        { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B' },
      ],
    },
    {
      id: 'anthropic',
      name: 'Anthropic (Claude)',
      envKey: 'ANTHROPIC_API_KEY',
      models: [
        { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet' },
        { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku' },
      ],
    },
    {
      id: 'ollama',
      name: 'Ollama (Local)',
      envKey: 'OLLAMA_AVAILABLE',
      models: [
        { id: 'llama3.1:8b', name: 'Llama 3.1 8B (Local)' },
        { id: 'llama3.1:70b', name: 'Llama 3.1 70B (Local)' },
        { id: 'gemma2:9b', name: 'Gemma 2 9B (Local)' },
        { id: 'qwen2.5:7b', name: 'Qwen 2.5 7B (Local)' },
        { id: 'codellama:7b', name: 'Code Llama 7B (Local)' },
      ],
    },
  ];

  return SUPPORTED_MODELS.map(provider => {
    let isConfigured = false;
    
    if (provider.id === 'ollama') {
      isConfigured = true; // Assume Ollama is available
    } else {
      isConfigured = !!process.env[provider.envKey];
    }
    
    return { ...provider, configured: isConfigured };
  });
}

async function testSettingsConfiguration() {
  console.log('🔍 Testing Settings Page Model Configuration...\n');
  
  const availableModels = getAvailableModels();
  
  console.log('📊 Available Model Providers:');
  availableModels.forEach(provider => {
    const status = provider.configured ? '✅' : '❌';
    console.log(`\n${status} ${provider.name} (${provider.id})`);
    console.log(`   Environment Key: ${provider.envKey}`);
    console.log(`   Configured: ${provider.configured}`);
    console.log(`   Models Available: ${provider.models.length}`);
    
    provider.models.forEach(model => {
      console.log(`     - ${model.name} (${model.id})`);
    });
  });
  
  console.log('\n🎯 Configuration Summary:');
  const configuredProviders = availableModels.filter(p => p.configured);
  const totalModels = availableModels.reduce((sum, p) => sum + p.models.length, 0);
  const availableModelsCount = configuredProviders.reduce((sum, p) => sum + p.models.length, 0);
  
  console.log(`   Total Providers: ${availableModels.length}`);
  console.log(`   Configured Providers: ${configuredProviders.length}`);
  console.log(`   Total Models: ${totalModels}`);
  console.log(`   Available Models: ${availableModelsCount}`);
  
  console.log('\n🔧 Settings Page Test Cases:');
  
  // Test case 1: Amanda's current configuration
  console.log('\n1. Amanda Agent Configuration:');
  const amandaModel = { provider: 'googleai', modelId: 'gemini-1.5-flash-latest' };
  const amandaProvider = availableModels.find(p => p.id === amandaModel.provider);
  const amandaModelInfo = amandaProvider?.models.find(m => m.id === amandaModel.modelId);
  
  console.log(`   Current Provider: ${amandaProvider?.name || 'Unknown'}`);
  console.log(`   Current Model: ${amandaModelInfo?.name || 'Unknown'}`);
  console.log(`   Provider Configured: ${amandaProvider?.configured || false}`);
  
  // Test case 2: Cody's current configuration
  console.log('\n2. Cody Agent Configuration:');
  const codyModel = { provider: 'groq', modelId: 'llama-3.1-8b-instant' };
  const codyProvider = availableModels.find(p => p.id === codyModel.provider);
  const codyModelInfo = codyProvider?.models.find(m => m.id === codyModel.modelId);
  
  console.log(`   Current Provider: ${codyProvider?.name || 'Unknown'}`);
  console.log(`   Current Model: ${codyModelInfo?.name || 'Unknown'}`);
  console.log(`   Provider Configured: ${codyProvider?.configured || false}`);
  
  // Test case 3: Model switching simulation
  console.log('\n3. Model Switching Test:');
  console.log('   Available providers for switching:');
  configuredProviders.forEach(provider => {
    console.log(`     ${provider.name}: ${provider.models.length} models`);
  });
  
  console.log('\n✅ Settings Configuration Test Complete!');
  console.log('\n💡 Expected Behavior:');
  console.log('   - Agent selection should work by clicking on agent names');
  console.log('   - Provider dropdown should show all 5 providers');
  console.log('   - Model dropdown should populate based on selected provider');
  console.log('   - Save button should persist changes to agent-settings.json');
  console.log('   - Only configured providers should be selectable');
}

testSettingsConfiguration().catch(console.error);
