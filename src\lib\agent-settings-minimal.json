[{"id": "Mq57tleDBanDxipt", "name": "Cody-TEST", "description": "A senior-level AI coding assistant and mentor who helps solve programming and technical problems.", "systemMessage": "You are <PERSON>, a senior-level AI coding assistant and mentor. You have access to a comprehensive knowledge base with programming documentation. Always use your tools to search for information and generate code based on best practices.", "icon": "Code", "webhookId": "", "tools": [{"id": "f391h97g-cody-tool", "name": "<PERSON>'s Knowledge Base", "description": "Searches a vector database for coding-related documents and information.", "icon": "DatabaseZap", "enabled": true}], "activity": [{"id": "act-cody-1", "step": "Listening", "timestamp": "Ready", "details": "Awaiting user prompt."}], "model": {"provider": "groq", "modelId": "llama-3.1-8b-instant"}}, {"id": "x3M1ATR5WY16vkPd", "name": "Li-TEST", "description": "An expert professional email communication specialist who composes polished, professional emails.", "systemMessage": "You are <PERSON>, an expert professional email communication specialist. You help users compose professional emails with proper etiquette and formatting.", "icon": "Mail", "webhookId": "", "tools": [{"id": "a746c42a-07d3-4f25-9a0f-2e070d53ee42", "name": "Gmail", "description": "Sends an email to a specified recipient.", "icon": "<PERSON><PERSON>", "enabled": true}], "activity": [{"id": "act-li-1", "step": "Listening", "timestamp": "Ready", "details": "Awaiting user prompt."}], "model": {"provider": "openai", "modelId": "gpt-4o-mini"}}, {"id": "NeK3H8lDKNXHFHVV", "name": "<PERSON>", "description": "A compassionate, emotionally intelligent AI therapist.", "systemMessage": "You are <PERSON>, a compassionate AI therapist.", "icon": "Heart", "webhookId": "", "tools": [{"id": "f391h97g-amanda-tool", "name": "Amanda's Knowledge Base", "description": "Searches a vector database for mental health-related documents and information.", "icon": "DatabaseZap", "enabled": true}], "activity": [{"id": "act-amanda-1", "step": "Listening", "timestamp": "Ready", "details": "Awaiting user prompt."}], "model": {"provider": "go<PERSON><PERSON><PERSON>", "modelId": "gemini-1.5-flash-latest"}}]