// Test JSON validation for agent settings
const fs = require('fs').promises;
const path = require('path');

async function testJSONValidation() {
  console.log('🔍 Testing JSON Validation for Agent Settings...\n');
  
  try {
    // Load and parse the JSON file
    const settingsPath = path.join(__dirname, 'src', 'lib', 'agent-settings.json');
    const settingsContent = await fs.readFile(settingsPath, 'utf-8');
    
    console.log('📄 File loaded successfully');
    console.log(`📊 File size: ${settingsContent.length} characters`);
    
    // Test JSON parsing
    let agents;
    try {
      agents = JSON.parse(settingsContent);
      console.log('✅ JSON parsing successful');
      console.log(`📊 Agents found: ${agents.length}`);
    } catch (parseError) {
      console.log('❌ JSON parsing failed:', parseError.message);
      return;
    }
    
    // Test each agent individually
    console.log('\n🔍 Testing Individual Agents:');
    
    agents.forEach((agent, index) => {
      console.log(`\n${index + 1}. Testing ${agent.name}:`);
      
      try {
        // Test JSON serialization
        const serialized = JSON.stringify(agent);
        console.log(`   ✅ JSON serialization: OK (${serialized.length} chars)`);
        
        // Test re-parsing
        const reparsed = JSON.parse(serialized);
        console.log(`   ✅ JSON re-parsing: OK`);
        
        // Test specific fields
        const requiredFields = ['id', 'name', 'description', 'systemMessage', 'icon', 'model'];
        const missingFields = requiredFields.filter(field => !agent[field]);
        
        if (missingFields.length === 0) {
          console.log(`   ✅ Required fields: All present`);
        } else {
          console.log(`   ❌ Missing fields: ${missingFields.join(', ')}`);
        }
        
        // Test systemMessage for problematic characters
        if (agent.systemMessage) {
          const hasProblematicChars = /[\u0000-\u001F\u007F-\u009F]/.test(agent.systemMessage);
          if (hasProblematicChars) {
            console.log(`   ⚠️  SystemMessage contains control characters`);
          } else {
            console.log(`   ✅ SystemMessage: Clean`);
          }
          
          console.log(`   📏 SystemMessage length: ${agent.systemMessage.length} chars`);
        }
        
        // Test model configuration
        if (agent.model && agent.model.provider && agent.model.modelId) {
          console.log(`   ✅ Model config: ${agent.model.provider}/${agent.model.modelId}`);
        } else {
          console.log(`   ❌ Model config: Invalid or missing`);
        }
        
        // Test tools array
        if (Array.isArray(agent.tools)) {
          console.log(`   ✅ Tools: Array with ${agent.tools.length} items`);
        } else {
          console.log(`   ❌ Tools: Not an array`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error testing ${agent.name}: ${error.message}`);
      }
    });
    
    // Test specific problematic agents
    console.log('\n🎯 Focus on Cody and Li:');
    
    const cody = agents.find(a => a.name === 'Cody');
    const li = agents.find(a => a.name === 'Li');
    
    if (cody) {
      console.log('\n📝 Cody Detailed Analysis:');
      console.log(`   ID: "${cody.id}"`);
      console.log(`   Name: "${cody.name}"`);
      console.log(`   Icon: "${cody.icon}"`);
      console.log(`   SystemMessage length: ${cody.systemMessage.length}`);
      console.log(`   SystemMessage preview: ${cody.systemMessage.substring(0, 100)}...`);
      
      // Check for specific characters that might cause issues
      const problematicPatterns = [
        { name: 'Newlines', pattern: /\n/g },
        { name: 'Quotes', pattern: /"/g },
        { name: 'Backslashes', pattern: /\\/g },
        { name: 'Unicode', pattern: /[^\x00-\x7F]/g }
      ];
      
      problematicPatterns.forEach(({ name, pattern }) => {
        const matches = cody.systemMessage.match(pattern);
        if (matches) {
          console.log(`   📊 ${name}: ${matches.length} occurrences`);
        }
      });
    }
    
    if (li) {
      console.log('\n📝 Li Detailed Analysis:');
      console.log(`   ID: "${li.id}"`);
      console.log(`   Name: "${li.name}"`);
      console.log(`   Icon: "${li.icon}"`);
      console.log(`   SystemMessage length: ${li.systemMessage.length}`);
      console.log(`   SystemMessage preview: ${li.systemMessage.substring(0, 100)}...`);
    }
    
    // Test re-writing the file
    console.log('\n💾 Testing File Write:');
    try {
      const testContent = JSON.stringify(agents, null, 2);
      const testPath = settingsPath + '.test';
      await fs.writeFile(testPath, testContent, 'utf-8');
      console.log('   ✅ File write successful');
      
      // Clean up test file
      await fs.unlink(testPath);
      console.log('   ✅ Test file cleaned up');
    } catch (writeError) {
      console.log('   ❌ File write failed:', writeError.message);
    }
    
    console.log('\n✅ JSON Validation Complete!');
    
  } catch (error) {
    console.log('❌ Error during JSON validation:', error.message);
  }
}

testJSONValidation().catch(console.error);
