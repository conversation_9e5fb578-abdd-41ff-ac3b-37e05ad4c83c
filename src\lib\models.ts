import type { Agent } from './types';

export interface ModelInfo {
  id: string;
  name: string;
}

export interface ModelProvider {
  id: string;
  name: string;
  models: ModelInfo[];
  envKey: string;
  configured?: boolean;
}

const SUPPORTED_MODELS: ModelProvider[] = [
  {
    id: 'googleai',
    name: 'Google AI (Gemini)',
    envKey: 'GEMINI_API_KEY',
    models: [
      { id: 'gemini-1.5-flash-latest', name: 'Gemini 1.5 Flash' },
      { id: 'gemini-1.5-pro-latest', name: 'Gemini 1.5 Pro' },
      { id: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash (Experimental)' },
    ],
  },
  {
    id: 'openai',
    name: 'OpenAI (GPT)',
    envKey: 'OPENAI_API_KEY',
    models: [
      { id: 'gpt-4o', name: 'GPT-4o' },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
    ],
  },
  {
    id: 'groq',
    name: '<PERSON>roq (Fast Inference)',
    envKey: 'GROQ_API_KEY',
    models: [
      { id: 'llama-3.1-70b-versatile', name: 'Llama 3.1 70B' },
      { id: 'llama-3.1-8b-instant', name: 'Llama 3.1 8B' },
      { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B' },
    ],
  },
  {
    id: 'anthropic',
    name: 'Anthropic (Claude)',
    envKey: 'ANTHROPIC_API_KEY',
    models: [
      { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet' },
      { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku' },
    ],
  },
  {
    id: 'ollama',
    name: 'Ollama (Local)',
    envKey: 'OLLAMA_AVAILABLE', // Special case - always available if running
    models: [
      { id: 'llama3.1:8b', name: 'Llama 3.1 8B (Local)' },
      { id: 'llama3.1:70b', name: 'Llama 3.1 70B (Local)' },
      { id: 'gemma2:9b', name: 'Gemma 2 9B (Local)' },
      { id: 'qwen2.5:7b', name: 'Qwen 2.5 7B (Local)' },
      { id: 'codellama:7b', name: 'Code Llama 7B (Local)' },
    ],
  },
];


export function getAvailableModels(): ModelProvider[] {
  // Return all models, but add a flag indicating if they are configured
  return SUPPORTED_MODELS.map(provider => {
    let isConfigured = false;

    if (typeof process !== 'undefined' && process.env) {
      if (provider.id === 'ollama') {
        // For Ollama, we assume it's available (user can test connection separately)
        isConfigured = true;
      } else {
        // For API providers, check if the API key is set
        isConfigured = !!process.env[provider.envKey];
      }
    }

    return { ...provider, configured: isConfigured };
  });
}


// Helper function to get model name for display
export function getModelDisplayName(agent: Agent): string {
    const provider = SUPPORTED_MODELS.find(p => p.id === agent.model.provider);
    if (!provider) return 'Unknown Model';

    const model = provider.models.find(m => m.id === agent.model.modelId);
    return model ? `${provider.name} / ${model.name}` : 'Unknown Model';
}
