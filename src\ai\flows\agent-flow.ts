'use server';
/**
 * @fileOverview A unified flow for running any agent.
 */

import { ai } from '@/ai/genkit';
import { getAgentById } from '@/lib/data';
import { z } from 'genkit';
import { toolRegistry } from '../tools';
import type { GenkitTool } from 'genkit/tool';
import { googleAI } from '@genkit-ai/googleai';

const AgentFlowInputSchema = z.object({
  agentId: z.string().describe('The ID of the agent to run.'),
  prompt: z.string().describe('The user message to send to the agent.'),
  sessionId: z.string().describe('A unique identifier for the chat session.'),
});
export type AgentFlowInput = z.infer<typeof AgentFlowInputSchema>;

const AgentFlowOutputSchema = z.object({
  response: z.string().describe("The agent's response."),
});
export type AgentFlowOutput = z.infer<typeof AgentFlowOutputSchema>;

export async function agentFlow(input: AgentFlowInput): Promise<AgentFlowOutput> {
  return agentFlowRunner(input);
}

const agentFlowRunner = ai.defineFlow(
  {
    name: 'agentFlow',
    inputSchema: AgentFlowInputSchema,
    outputSchema: AgentFlowOutputSchema,
  },
  async ({ agentId, prompt, sessionId }) => {
    const agent = getAgentById(agentId);
    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`);
    }

    let model;
    if (agent.model.provider === 'googleai') {
      // Use typed model reference for Google AI
      model = googleAI.model(agent.model.modelId);
    } else {
      // Use string-based model references for all other providers
      model = `${agent.model.provider}/${agent.model.modelId}`;
    }
    
    const agentTools = (agent.tools || [])
      .map(t => toolRegistry[t.name as keyof typeof toolRegistry])
      .filter(t => !!t) as GenkitTool[];

    const { text } = await ai.generate({
      model: model,
      system: agent.systemMessage,
      prompt: prompt,
      tools: agentTools,
    });

    // NOTE: Chat history has been disabled to force the agent to use its knowledge base tool.
    // This makes the agent stateless between turns to resolve an issue where it was
    // querying its own chat history instead of its designated RAG database.

    return { response: text };
  }
);
