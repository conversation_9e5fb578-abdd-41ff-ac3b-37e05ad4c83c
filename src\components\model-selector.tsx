'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { availableModels, getModelsByProvider, getProviders, type ModelConfig } from '@/lib/model-config';
import { Check, Zap, Eye, Wrench, DollarSign } from 'lucide-react';

interface ModelSelectorProps {
  currentModel: {
    provider: string;
    modelId: string;
  };
  onModelChange: (provider: string, modelId: string) => void;
}

export function ModelSelector({ currentModel, onModelChange }: ModelSelectorProps) {
  const [selectedProvider, setSelectedProvider] = useState(currentModel.provider);
  const [selectedModel, setSelectedModel] = useState(currentModel.modelId);
  
  // Sync local state with prop changes
  useEffect(() => {
    setSelectedProvider(currentModel.provider);
    setSelectedModel(currentModel.modelId);
  }, [currentModel.provider, currentModel.modelId]);
  
  const providers = getProviders();
  const modelsForProvider = getModelsByProvider(selectedProvider);
  
  const handleProviderChange = (provider: string) => {
    setSelectedProvider(provider);
    const firstModel = getModelsByProvider(provider)[0];
    if (firstModel) {
      setSelectedModel(firstModel.id);
    }
  };
  
  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
  };
  
  const handleSave = () => {
    console.log('Saving model:', { provider: selectedProvider, modelId: selectedModel });
    onModelChange(selectedProvider, selectedModel);
  };
  
  const selectedModelConfig = modelsForProvider.find(m => m.id === selectedModel);
  
  // Check if current selection is different from current model
  const hasChanges = selectedProvider !== currentModel.provider || selectedModel !== currentModel.modelId;
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Provider</label>
          <Select value={selectedProvider} onValueChange={handleProviderChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="googleai">Google AI (Gemini)</SelectItem>
              <SelectItem value="openai">OpenAI (GPT)</SelectItem>
              <SelectItem value="groq">Groq (Fast Inference)</SelectItem>
              <SelectItem value="anthropic">Anthropic (Claude)</SelectItem>
              <SelectItem value="deepseek">DeepSeek</SelectItem>
              <SelectItem value="ollama">Ollama (Local)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <label className="text-sm font-medium mb-2 block">Model</label>
          <Select value={selectedModel} onValueChange={handleModelChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent>
              {modelsForProvider.map((model) => (
                <SelectItem key={model.id} value={model.id}>
                  {model.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {selectedModelConfig && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {selectedModelConfig.name}
              {selectedModelConfig.provider === currentModel.provider && 
               selectedModelConfig.id === currentModel.modelId && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Check className="h-3 w-3" />
                  Current
                </Badge>
              )}
            </CardTitle>
            <CardDescription>{selectedModelConfig.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {(selectedModelConfig.contextWindow / 1000).toFixed(0)}K
                </div>
                <div className="text-xs text-muted-foreground">Context Window</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {(selectedModelConfig.maxOutputTokens / 1000).toFixed(0)}K
                </div>
                <div className="text-xs text-muted-foreground">Max Output</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {selectedModelConfig.costPer1kTokens ? 
                    `$${selectedModelConfig.costPer1kTokens.input}` : 'Free'}
                </div>
                <div className="text-xs text-muted-foreground">Cost/1K Input</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {selectedModelConfig.costPer1kTokens ? 
                    `$${selectedModelConfig.costPer1kTokens.output}` : 'Free'}
                </div>
                <div className="text-xs text-muted-foreground">Cost/1K Output</div>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {selectedModelConfig.supportsTools && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Wrench className="h-3 w-3" />
                  Tool Calling
                </Badge>
              )}
              {selectedModelConfig.supportsVision && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  Vision
                </Badge>
              )}
              {selectedModelConfig.provider === 'groq' && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Zap className="h-3 w-3" />
                  Fast Inference
                </Badge>
              )}
              {selectedModelConfig.provider === 'ollama' && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <DollarSign className="h-3 w-3" />
                  No API Costs
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}
      
      <Tabs defaultValue="recommended" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="recommended">Recommended</TabsTrigger>
          <TabsTrigger value="all">All Models</TabsTrigger>
          <TabsTrigger value="local">Local Only</TabsTrigger>
        </TabsList>
        
        <TabsContent value="recommended" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              availableModels.find(m => m.id === 'gemini-1.5-flash-latest'),
              availableModels.find(m => m.id === 'gpt-4o-mini'),
              availableModels.find(m => m.id === 'llama-3.1-8b-instant'),
              availableModels.find(m => m.id === 'claude-3-5-haiku-20241022'),
              availableModels.find(m => m.id === 'llama3.1:8b'),
              availableModels.find(m => m.id === 'deepseek-chat'),
            ].filter(Boolean).map((model) => (
              <ModelCard 
                key={`${model!.provider}-${model!.id}`}
                model={model!} 
                isSelected={selectedProvider === model!.provider && selectedModel === model!.id}
                isCurrent={currentModel.provider === model!.provider && currentModel.modelId === model!.id}
                onSelect={() => {
                  setSelectedProvider(model!.provider);
                  setSelectedModel(model!.id);
                }}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableModels.map((model) => (
              <ModelCard 
                key={`${model.provider}-${model.id}`}
                model={model} 
                isSelected={selectedProvider === model.provider && selectedModel === model.id}
                isCurrent={currentModel.provider === model.provider && currentModel.modelId === model.id}
                onSelect={() => {
                  setSelectedProvider(model.provider);
                  setSelectedModel(model.id);
                }}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="local" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableModels.filter(m => m.provider === 'ollama').map((model) => (
              <ModelCard 
                key={`${model.provider}-${model.id}`}
                model={model} 
                isSelected={selectedProvider === model.provider && selectedModel === model.id}
                isCurrent={currentModel.provider === model.provider && currentModel.modelId === model.id}
                onSelect={() => {
                  setSelectedProvider(model.provider);
                  setSelectedModel(model.id);
                }}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
      
      <div className="flex justify-end gap-2">
        <Button 
          onClick={handleSave}
          disabled={!hasChanges}
        >
          {hasChanges ? 'Update Model' : 'No Changes'}
        </Button>
      </div>
    </div>
  );
}

interface ModelCardProps {
  model: ModelConfig;
  isSelected: boolean;
  isCurrent: boolean;
  onSelect: () => void;
}

function ModelCard({ model, isSelected, isCurrent, onSelect }: ModelCardProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-blue-500' : ''
      } ${isCurrent ? 'bg-green-50 border-green-200' : ''}`}
      onClick={onSelect}
    >
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center justify-between">
          {model.name}
          {isCurrent && (
            <Badge variant="secondary" className="text-xs">
              Current
            </Badge>
          )}
        </CardTitle>
        <CardDescription className="text-xs">{model.description}</CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex flex-wrap gap-1 mb-2">
          {model.supportsTools && <Badge variant="outline" className="text-xs">Tools</Badge>}
          {model.supportsVision && <Badge variant="outline" className="text-xs">Vision</Badge>}
          {model.provider === 'ollama' && <Badge variant="outline" className="text-xs">Local</Badge>}
        </div>
        <div className="text-xs text-muted-foreground">
          {(model.contextWindow / 1000).toFixed(0)}K context • {(model.maxOutputTokens / 1000).toFixed(0)}K output
        </div>
      </CardContent>
    </Card>
  );
}