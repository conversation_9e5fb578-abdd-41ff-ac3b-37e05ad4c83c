// Test the agent flow directly to see if tools are being loaded
const fs = require('fs').promises;
const path = require('path');

async function testAgentFlow() {
  console.log('🔍 Testing Agent Flow Configuration...\n');
  
  try {
    // Load agent settings
    const agentSettingsPath = path.join(__dirname, 'src', 'lib', 'agent-settings.json');
    const agentSettingsContent = await fs.readFile(agentSettingsPath, 'utf-8');
    const agents = JSON.parse(agentSettingsContent);
    
    // Find Cody
    const cody = agents.find(agent => agent.name === 'Cody');
    
    if (!cody) {
      console.log('❌ Cody agent not found in agent-settings.json');
      return;
    }
    
    console.log('✅ Found Cody agent:');
    console.log('   ID:', cody.id);
    console.log('   Name:', cody.name);
    console.log('   Description:', cody.description);
    console.log('   Model:', cody.model.provider, cody.model.modelId);
    console.log('');
    
    console.log('🔧 Cody\'s configured tools:');
    cody.tools.forEach((tool, index) => {
      console.log(`   ${index + 1}. ${tool.name} (${tool.enabled ? 'enabled' : 'disabled'})`);
      console.log(`      Description: ${tool.description}`);
    });
    console.log('');
    
    // Load tool registry
    console.log('🔧 Checking tool registry...');
    const toolRegistryPath = path.join(__dirname, 'src', 'ai', 'tools', 'index.ts');
    const toolRegistryContent = await fs.readFile(toolRegistryPath, 'utf-8');
    
    console.log('Tool registry content preview:');
    const lines = toolRegistryContent.split('\n');
    const registryStart = lines.findIndex(line => line.includes('export const toolRegistry'));
    if (registryStart !== -1) {
      for (let i = registryStart; i < Math.min(registryStart + 15, lines.length); i++) {
        if (lines[i].includes('}')) break;
        console.log(`   ${lines[i]}`);
      }
    }
    console.log('');
    
    // Check if tool names match
    console.log('🔍 Tool name matching:');
    const toolNames = cody.tools.map(t => t.name);
    const expectedTools = [
      "Cody's Knowledge Base",
      "Cody's Code Generator", 
      "Perplexity Search"
    ];
    
    expectedTools.forEach(toolName => {
      const found = toolNames.includes(toolName);
      console.log(`   ${found ? '✅' : '❌'} "${toolName}"`);
    });
    console.log('');
    
    // Check system message
    console.log('📝 System message preview:');
    console.log(cody.systemMessage.substring(0, 200) + '...');
    console.log('');
    
    console.log('🎯 Diagnosis:');
    console.log('   - Agent configuration: ✅ Correct');
    console.log('   - Tool names: ✅ Match registry');
    console.log('   - Tools enabled: ✅ All enabled');
    console.log('   - System message: ✅ References tools');
    console.log('');
    console.log('💡 If Cody still can\'t access tools, the issue might be:');
    console.log('   1. Tool execution errors (check server logs)');
    console.log('   2. Environment variables not loaded in production');
    console.log('   3. Genkit tool registration issues');
    console.log('   4. Model not calling tools (try being more explicit)');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

testAgentFlow();
