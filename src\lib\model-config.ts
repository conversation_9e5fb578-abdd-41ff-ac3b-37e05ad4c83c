// Model configuration for all supported LLM providers
export interface ModelConfig {
  id: string;
  name: string;
  provider: string;
  description: string;
  contextWindow: number;
  maxOutputTokens: number;
  supportsTools: boolean;
  supportsVision: boolean;
  costPer1kTokens?: {
    input: number;
    output: number;
  };
}

export const availableModels: ModelConfig[] = [
  // Google AI (Gemini) Models
  {
    id: 'gemini-1.5-flash-latest',
    name: 'Gemini 1.5 Flash',
    provider: 'googleai',
    description: 'Fast and efficient model for most tasks',
    contextWindow: 1000000,
    maxOutputTokens: 8192,
    supportsTools: true,
    supportsVision: true,
    costPer1kTokens: { input: 0.075, output: 0.30 }
  },
  {
    id: 'gemini-1.5-pro-latest',
    name: 'Gemini 1.5 Pro',
    provider: 'googleai',
    description: 'Most capable model for complex reasoning',
    contextWindow: 2000000,
    maxOutputTokens: 8192,
    supportsTools: true,
    supportsVision: true,
    costPer1kTokens: { input: 1.25, output: 5.00 }
  },
  {
    id: 'gemini-2.0-flash-exp',
    name: 'Gemini 2.0 Flash (Experimental)',
    provider: 'googleai',
    description: 'Latest experimental model with enhanced capabilities',
    contextWindow: 1000000,
    maxOutputTokens: 8192,
    supportsTools: true,
    supportsVision: true,
    costPer1kTokens: { input: 0.075, output: 0.30 }
  },

  // OpenAI Models
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'openai',
    description: 'Most advanced multimodal model',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    supportsTools: true,
    supportsVision: true,
    costPer1kTokens: { input: 2.50, output: 10.00 }
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'openai',
    description: 'Affordable and intelligent small model',
    contextWindow: 128000,
    maxOutputTokens: 16384,
    supportsTools: true,
    supportsVision: true,
    costPer1kTokens: { input: 0.15, output: 0.60 }
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'openai',
    description: 'Fast and cost-effective model',
    contextWindow: 16385,
    maxOutputTokens: 4096,
    supportsTools: true,
    supportsVision: false,
    costPer1kTokens: { input: 0.50, output: 1.50 }
  },

  // Groq Models (Fast inference)
  {
    id: 'llama-3.1-70b-versatile',
    name: 'Llama 3.1 70B',
    provider: 'groq',
    description: 'Large model with excellent reasoning (Groq fast inference)',
    contextWindow: 131072,
    maxOutputTokens: 8000,
    supportsTools: true,
    supportsVision: false,
    costPer1kTokens: { input: 0.59, output: 0.79 }
  },
  {
    id: 'llama-3.1-8b-instant',
    name: 'Llama 3.1 8B',
    provider: 'groq',
    description: 'Fast and efficient model (Groq ultra-fast inference)',
    contextWindow: 131072,
    maxOutputTokens: 8000,
    supportsTools: true,
    supportsVision: false,
    costPer1kTokens: { input: 0.05, output: 0.08 }
  },
  {
    id: 'mixtral-8x7b-32768',
    name: 'Mixtral 8x7B',
    provider: 'groq',
    description: 'Mixture of experts model (Groq fast inference)',
    contextWindow: 32768,
    maxOutputTokens: 32768,
    supportsTools: true,
    supportsVision: false,
    costPer1kTokens: { input: 0.24, output: 0.24 }
  },

  // Anthropic Models (Claude)
  {
    id: 'claude-3-5-sonnet-20241022',
    name: 'Claude 3.5 Sonnet',
    provider: 'anthropic',
    description: 'Most intelligent model with excellent reasoning',
    contextWindow: 200000,
    maxOutputTokens: 8192,
    supportsTools: true,
    supportsVision: true,
    costPer1kTokens: { input: 3.00, output: 15.00 }
  },
  {
    id: 'claude-3-5-haiku-20241022',
    name: 'Claude 3.5 Haiku',
    provider: 'anthropic',
    description: 'Fast and cost-effective model',
    contextWindow: 200000,
    maxOutputTokens: 8192,
    supportsTools: true,
    supportsVision: true,
    costPer1kTokens: { input: 0.80, output: 4.00 }
  },

  // DeepSeek Models
  {
    id: 'deepseek-chat',
    name: 'DeepSeek Chat',
    provider: 'deepseek',
    description: 'Advanced reasoning model at low cost',
    contextWindow: 64000,
    maxOutputTokens: 8192,
    supportsTools: true,
    supportsVision: false,
    costPer1kTokens: { input: 0.14, output: 0.28 }
  },
  {
    id: 'deepseek-coder',
    name: 'DeepSeek Coder',
    provider: 'deepseek',
    description: 'Specialized coding model',
    contextWindow: 64000,
    maxOutputTokens: 8192,
    supportsTools: true,
    supportsVision: false,
    costPer1kTokens: { input: 0.14, output: 0.28 }
  },

  // Ollama Models (Local)
  {
    id: 'llama3.1:8b',
    name: 'Llama 3.1 8B (Local)',
    provider: 'ollama',
    description: 'Local Llama model - no API costs',
    contextWindow: 131072,
    maxOutputTokens: 8000,
    supportsTools: true,
    supportsVision: false,
  },
  {
    id: 'llama3.1:70b',
    name: 'Llama 3.1 70B (Local)',
    provider: 'ollama',
    description: 'Large local Llama model - no API costs',
    contextWindow: 131072,
    maxOutputTokens: 8000,
    supportsTools: true,
    supportsVision: false,
  },
  {
    id: 'gemma2:9b',
    name: 'Gemma 2 9B (Local)',
    provider: 'ollama',
    description: 'Google\'s open model running locally',
    contextWindow: 8192,
    maxOutputTokens: 4096,
    supportsTools: true,
    supportsVision: false,
  },
  {
    id: 'qwen2.5:7b',
    name: 'Qwen 2.5 7B (Local)',
    provider: 'ollama',
    description: 'Alibaba\'s multilingual model',
    contextWindow: 32768,
    maxOutputTokens: 8192,
    supportsTools: true,
    supportsVision: false,
  },
  {
    id: 'codellama:7b',
    name: 'Code Llama 7B (Local)',
    provider: 'ollama',
    description: 'Specialized coding model running locally',
    contextWindow: 16384,
    maxOutputTokens: 4096,
    supportsTools: true,
    supportsVision: false,
  }
];

// Helper functions
export function getModelsByProvider(provider: string): ModelConfig[] {
  return availableModels.filter(model => model.provider === provider);
}

export function getModelById(id: string, provider: string): ModelConfig | undefined {
  return availableModels.find(model => model.id === id && model.provider === provider);
}

export function getProviders(): string[] {
  return [...new Set(availableModels.map(model => model.provider))];
}

export function getRecommendedModels(): ModelConfig[] {
  return [
    availableModels.find(m => m.id === 'gemini-1.5-flash-latest')!,
    availableModels.find(m => m.id === 'gpt-4o-mini')!,
    availableModels.find(m => m.id === 'llama-3.1-8b-instant')!,
    availableModels.find(m => m.id === 'claude-3-5-haiku-20241022')!,
    availableModels.find(m => m.id === 'llama3.1:8b')!,
  ].filter(Boolean);
}
